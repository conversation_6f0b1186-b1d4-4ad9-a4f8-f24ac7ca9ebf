import { Column, <PERSON>tity, PrimaryGeneratedColumn, ManyToOne, Join<PERSON>olumn } from 'typeorm';
import { People } from 'src/people/people.entity';

@Entity()
export class JobPreferences {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    nullable: true,
    default: null,
  })
  job_title: string;

  @Column({
    nullable: true,
    default: null,
  })
  job_location: string;

  @Column({
    type: 'enum',
    enum: ['PERMANENT', 'CONTRACT', 'TEMPORARY', 'FREELANCE'],
    nullable: true,
    default: null,
  })
  job_type: string; // Permanent, Contract

  @Column({
    nullable: true,
    default: null,
  })
  job_industry: string;

  @Column({
    nullable: true,
    default: null,
  })
  job_experience: string;

  @Column({
    nullable: true,
    default: null,
  })
  job_salary: string;

  @Column({
    nullable: true,
    default: null,
  })
  job_skills: string;

  @Column({
    nullable: true,
    default: null,
  })
  salary_range_start: number;

  @Column({
    nullable: true,
    default: null,
  })
  salary_range_end: number;

  @Column({
    nullable: true,
    default: null,
  })
  salary_currency: string;

  @Column({
    nullable: true,
    type: 'enum',
    enum: ['DAILY', 'WEEKLY', 'MONTHLY', 'ANNUALLY'],
    default: 'ANNUALLY',
  })
  salary_period: string;

  @Column({
    nullable: true,
    default: null,
  })
  job_location_zip: string;

  @Column({
    nullable: true,
    default: null,
  })
  job_location_country: string;

  @Column({
    nullable: true,
    default: null,
  })
  job_location_city: string;

  @Column({
    nullable: true,
    default: null,
  })
  job_location_state: string;

  @Column({
    nullable: true,
    default: null,
  })
  job_location_address: string;

  @Column({
    nullable: true,
    default: null,
  })
  availability: string;

  @Column({
    nullable: true,
    default: null,
  })
  reason_for_leaving: string;

  @Column({
    type: 'simple-array',
    nullable: true,
    default: null,
  })
  looking_for_time: string[]; // Part-Time, Full-Time

  @Column({
    type: 'enum',
    enum: ['REMOTE', 'HYBRID', 'ON_SITE'],
    nullable: true,
    default: null,
  })
  job_nature: string; // Remote, Hybrid, On-Site

  @Column({
    nullable: true,
    default: null,
  })
  salary_range: string; // Combined salary range as string

  // Relationship with People entity
  @ManyToOne(() => People, (person) => person.jobPreferences, { nullable: true })
  @JoinColumn({ name: 'personId' })
  person: People;

  @Column({ nullable: true })
  personId: number;
}

