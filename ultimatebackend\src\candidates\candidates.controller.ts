import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CandidatesService } from './candidates.service';
import { CandidateDto, UpdateCandidateDto } from './dto/candidate.dto';

@ApiTags('candidates')
@Controller('candidates')
export class CandidatesController {
  constructor(private readonly candidatesService: CandidatesService) {}

  @Post('create')
  @ApiOperation({ 
    summary: 'Create a new candidate',
    description: 'Creates a new candidate with personal information and job preferences'
  })
  @ApiBody({
    type: CandidateDto,
    description: 'Candidate data payload',
    examples: {
      example1: {
        summary: 'Complete candidate example',
        value: {
          first_name: '<PERSON>',
          last_name: '<PERSON><PERSON>',
          contact: '<EMAIL>',
          li_profile: 'https://linkedin.com/in/johndoe',
          experience: '5 years',
          highest_education: 'Bachelor\'s Degree in Computer Science',
          currently_employed: true,
          current_job_title: 'Senior Software Engineer',
          salary_range: '$80,000 - $120,000',
          looking_for_time: ['Full-Time'],
          job_type: 'PERMANENT',
          job_nature: 'REMOTE',
          preferred_industry: 'Technology',
          additional_information: 'Experienced in React, Node.js, and AWS',
          job_location: 'New York, NY',
          job_title: 'Software Engineer',
          job_experience: '3-5 years',
          availability: 'Immediately',
          reason_for_leaving: 'Career growth'
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: 'Candidate created successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 123 },
        first_name: { type: 'string', example: 'John' },
        last_name: { type: 'string', example: 'Doe' },
        contact: { type: 'string', example: '<EMAIL>' },
        // ... other fields
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createCandidate(@Body() candidateDto: CandidateDto) {
    try {
      return await this.candidatesService.createCandidate(candidateDto);
    } catch (error) {
      this.handleError(error);
    }
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get all candidates',
    description: 'Retrieves a paginated list of all candidates'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 20)',
    example: 20
  })
  @ApiResponse({
    status: 200,
    description: 'Candidates retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              first_name: { type: 'string' },
              last_name: { type: 'string' },
              // ... other fields
            }
          }
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getAllCandidates(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
  ) {
    try {
      return await this.candidatesService.getAllCandidates(page, limit);
    } catch (error) {
      this.handleError(error);
    }
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Get candidate by ID',
    description: 'Retrieves a specific candidate by their ID'
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'Candidate ID',
    example: 123
  })
  @ApiResponse({
    status: 200,
    description: 'Candidate retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 123 },
        first_name: { type: 'string', example: 'John' },
        last_name: { type: 'string', example: 'Doe' },
        // ... other fields
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Candidate not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getCandidateById(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.candidatesService.getCandidateById(id);
    } catch (error) {
      this.handleError(error);
    }
  }

  @Put(':id')
  @ApiOperation({ 
    summary: 'Update candidate',
    description: 'Updates an existing candidate\'s information'
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'Candidate ID',
    example: 123
  })
  @ApiBody({
    type: UpdateCandidateDto,
    description: 'Updated candidate data (all fields are optional)',
    examples: {
      example1: {
        summary: 'Partial update example',
        value: {
          current_job_title: 'Lead Software Engineer',
          salary_range: '$90,000 - $140,000',
          job_nature: 'HYBRID',
          availability: 'In 2 weeks'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Candidate updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 123 },
        first_name: { type: 'string', example: 'John' },
        last_name: { type: 'string', example: 'Doe' },
        // ... other fields
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Candidate not found' })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async updateCandidate(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCandidateDto,
  ) {
    try {
      return await this.candidatesService.updateCandidate(id, updateDto);
    } catch (error) {
      this.handleError(error);
    }
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete candidate',
    description: 'Deletes a candidate and all associated data'
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'Candidate ID',
    example: 123
  })
  @ApiResponse({
    status: 200,
    description: 'Candidate deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Candidate deleted successfully' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Candidate not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async deleteCandidate(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.candidatesService.deleteCandidate(id);
    } catch (error) {
      this.handleError(error);
    }
  }

  // Bulk operations
  @Post('bulk')
  @ApiOperation({ 
    summary: 'Create multiple candidates',
    description: 'Creates multiple candidates in a single request'
  })
  @ApiBody({
    type: [CandidateDto],
    description: 'Array of candidate data',
  })
  @ApiResponse({
    status: 201,
    description: 'Candidates created successfully',
    schema: {
      type: 'object',
      properties: {
        created: { type: 'number', example: 5 },
        failed: { type: 'number', example: 0 },
        results: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: { type: 'object' },
              error: { type: 'string' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createMultipleCandidates(@Body() candidatesDto: CandidateDto[]) {
    try {
      const results = [];
      let created = 0;
      let failed = 0;

      for (const candidateDto of candidatesDto) {
        try {
          const result = await this.candidatesService.createCandidate(candidateDto);
          results.push({ success: true, data: result });
          created++;
        } catch (error) {
          results.push({ success: false, error: error.message });
          failed++;
        }
      }

      return {
        created,
        failed,
        results,
      };
    } catch (error) {
      this.handleError(error);
    }
  }

  private handleError(error: any): never {
    if (error.status) {
      throw error;
    }
    
    console.error('Candidates Controller Error:', error);
    throw new HttpException(
      'Internal server error',
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}
