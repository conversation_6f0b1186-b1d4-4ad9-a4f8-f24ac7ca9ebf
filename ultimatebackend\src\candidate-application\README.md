# Candidate Application Module

This module handles candidate resume submissions with automatic email notifications to the recruitment team.

## Features

- ✅ **Resume Submission**: Accept candidate applications with optional resume file upload
- ✅ **Email Notifications**: Automatically send emails to `<EMAIL>` and `<EMAIL>`
- ✅ **File Validation**: Validate resume files (PDF, DOC, DOCX, max 10MB)
- ✅ **S3 Integration**: Upload resume files to AWS S3 bucket
- ✅ **Confirmation Emails**: Send confirmation emails to candidates
- ✅ **Professional Templates**: HTML email templates with company branding

## API Endpoint

### POST `/candidate-applications/submit`

Submit a candidate application with optional resume file.

**Content-Type**: `multipart/form-data`

**Request Body**:
```json
{
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "contact": "<EMAIL>",
  "linkedin_profile": "https://linkedin.com/in/johndoe", // optional
  "job_location": "London, UK",
  "desired_job_role": "Software Engineer",
  "years_of_experience": "5 years",
  "availability": "Immediately",
  "reason_for_job_change": "Career growth and new challenges",
  "resume_file": "binary" // optional file upload
}
```

**Response**:
```json
{
  "message": "Application submitted successfully",
  "applicationId": "APP_1672531200000_abc123def"
}
```

## File Upload Requirements

- **Supported formats**: PDF, DOC, DOCX
- **Maximum size**: 10MB
- **Field name**: `resume_file`

## Email Notifications

### To Recruitment Team
- **Recipients**: `<EMAIL>`, `<EMAIL>`
- **Subject**: `New Candidate Application - [Job Role]`
- **Includes**: All application details, resume attachment (if provided)

### To Candidate (Confirmation)
- **Recipient**: Candidate's email address
- **Subject**: `Application Received - Ultimate Outsourcing`
- **Includes**: Application summary, next steps, contact information

## Environment Variables

Make sure these are configured in your `.env` file:

```env
# Email configuration
DEFAULT_FROM_EMAIL=<EMAIL>
AWS_SES_CAREERS_EMAIL=<EMAIL>

# AWS S3 configuration
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=eu-west-2
AWS_S3_BUCKET_NAME=s3ultimatebucket
```

## Usage Example

### Using cURL

```bash
curl -X POST http://localhost:5001/api/candidate-applications/submit \
  -F "first_name=John" \
  -F "last_name=Doe" \
  -F "contact=<EMAIL>" \
  -F "linkedin_profile=https://linkedin.com/in/johndoe" \
  -F "job_location=London, UK" \
  -F "desired_job_role=Software Engineer" \
  -F "years_of_experience=5 years" \
  -F "availability=Immediately" \
  -F "reason_for_job_change=Career growth" \
  -F "resume_file=@/path/to/resume.pdf"
```

### Using JavaScript/Fetch

```javascript
const formData = new FormData();
formData.append('first_name', 'John');
formData.append('last_name', 'Doe');
formData.append('contact', '<EMAIL>');
formData.append('linkedin_profile', 'https://linkedin.com/in/johndoe');
formData.append('job_location', 'London, UK');
formData.append('desired_job_role', 'Software Engineer');
formData.append('years_of_experience', '5 years');
formData.append('availability', 'Immediately');
formData.append('reason_for_job_change', 'Career growth');
formData.append('resume_file', fileInput.files[0]); // File from input

const response = await fetch('/api/candidate-applications/submit', {
  method: 'POST',
  body: formData
});

const result = await response.json();
console.log(result);
```

## Error Handling

The API returns appropriate HTTP status codes:

- **201**: Application submitted successfully
- **400**: Bad request (validation errors, invalid file format/size)
- **500**: Internal server error

## Testing

Run the unit tests:

```bash
npm test candidate-application.controller.spec.ts
```

## Dependencies

This module depends on:
- `EmailModule`: For sending emails via AWS SES
- `S3bucketModule`: For file uploads to AWS S3