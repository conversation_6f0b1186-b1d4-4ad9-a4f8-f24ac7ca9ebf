import { Test, TestingModule } from '@nestjs/testing';
import { CandidateApplicationController } from './candidate-application.controller';
import { CandidateApplicationService } from './candidate-application.service';
import { BadRequestException } from '@nestjs/common';

describe('CandidateApplicationController', () => {
  let controller: CandidateApplicationController;
  let service: CandidateApplicationService;

  const mockCandidateApplicationService = {
    submitApplication: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CandidateApplicationController],
      providers: [
        {
          provide: CandidateApplicationService,
          useValue: mockCandidateApplicationService,
        },
      ],
    }).compile();

    controller = module.get<CandidateApplicationController>(CandidateApplicationController);
    service = module.get<CandidateApplicationService>(CandidateApplicationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('submitApplication', () => {
    const mockApplicationData = {
      first_name: 'John',
      last_name: 'Doe',
      contact: '<EMAIL>',
      linkedin_profile: 'https://linkedin.com/in/johndoe',
      job_location: 'London, UK',
      desired_job_role: 'Software Engineer',
      years_of_experience: '5 years',
      availability: 'Immediately',
      reason_for_job_change: 'Career growth',
    };

    it('should submit application successfully without file', async () => {
      const expectedResult = {
        message: 'Application submitted successfully',
        applicationId: 'APP_123456789_abc123',
      };

      mockCandidateApplicationService.submitApplication.mockResolvedValue(expectedResult);

      const result = await controller.submitApplication(mockApplicationData);

      expect(result).toEqual(expectedResult);
      expect(service.submitApplication).toHaveBeenCalledWith(mockApplicationData, undefined);
    });

    it('should submit application successfully with valid file', async () => {
      const mockFile = {
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        size: 1024 * 1024, // 1MB
      } as Express.Multer.File;

      const expectedResult = {
        message: 'Application submitted successfully',
        applicationId: 'APP_123456789_abc123',
      };

      mockCandidateApplicationService.submitApplication.mockResolvedValue(expectedResult);

      const result = await controller.submitApplication(mockApplicationData, mockFile);

      expect(result).toEqual(expectedResult);
      expect(service.submitApplication).toHaveBeenCalledWith(mockApplicationData, mockFile);
    });

    it('should throw BadRequestException for invalid file size', async () => {
      const mockFile = {
        originalname: 'resume.pdf',
        mimetype: 'application/pdf',
        size: 15 * 1024 * 1024, // 15MB (exceeds 10MB limit)
      } as Express.Multer.File;

      await expect(
        controller.submitApplication(mockApplicationData, mockFile)
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for invalid file type', async () => {
      const mockFile = {
        originalname: 'resume.txt',
        mimetype: 'text/plain',
        size: 1024 * 1024, // 1MB
      } as Express.Multer.File;

      await expect(
        controller.submitApplication(mockApplicationData, mockFile)
      ).rejects.toThrow(BadRequestException);
    });
  });
});