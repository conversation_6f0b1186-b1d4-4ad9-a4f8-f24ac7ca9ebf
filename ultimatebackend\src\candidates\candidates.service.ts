import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { People } from 'src/people/people.entity';
import { JobPreferences } from 'src/preferences/prefrences.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { CandidateDto, UpdateCandidateDto } from './dto/candidate.dto';

@Injectable()
export class CandidatesService {
  constructor(
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    @InjectRepository(JobPreferences)
    private readonly jobPreferencesRepository: Repository<JobPreferences>,
    @InjectRepository(PersonEmail)
    private readonly personEmailRepository: Repository<PersonEmail>,
    @InjectRepository(PersonPhone)
    private readonly personPhoneRepository: Repository<PersonPhone>,
  ) {}

  async createCandidate(candidateDto: CandidateDto): Promise<any> {
    try {
      // Step 1: Create the Person entity
      const person = this.peopleRepository.create({
        first_name: candidateDto.first_name,
        last_name: candidateDto.last_name,
        full_name: `${candidateDto.first_name} ${candidateDto.last_name}`,
        current_title: candidateDto.current_job_title,
        profile_url: candidateDto.li_profile,
        currently_employed: candidateDto.currently_employed,
        additional_information: candidateDto.additional_information,
        person_type: 'CANDIDATE',
        industry: candidateDto.preferred_industry,
      });

      const savedPerson = await this.peopleRepository.save(person);

      // Step 2: Handle contact information (email/phone)
      if (candidateDto.contact) {
        await this.handleContactInformation(candidateDto.contact, savedPerson.id);
      }

      // Step 3: Create JobPreferences if any job-related data is provided
      const hasJobPreferences = this.hasJobPreferencesData(candidateDto);
      if (hasJobPreferences) {
        const jobPreferences = this.jobPreferencesRepository.create({
          job_title: candidateDto.job_title,
          job_location: candidateDto.job_location,
          job_type: candidateDto.job_type,
          job_industry: candidateDto.preferred_industry,
          job_experience: candidateDto.job_experience,
          availability: candidateDto.availability,
          reason_for_leaving: candidateDto.reason_for_leaving,
          looking_for_time: candidateDto.looking_for_time,
          job_nature: candidateDto.job_nature,
          salary_range: candidateDto.salary_range,
          personId: savedPerson.id,
        });

        await this.jobPreferencesRepository.save(jobPreferences);
      }

      // Step 4: Return the complete candidate data
      return await this.getCandidateById(savedPerson.id);
    } catch (error) {
      console.error('Error creating candidate:', error);
      throw new InternalServerErrorException('Failed to create candidate');
    }
  }

  async updateCandidate(candidateId: number, updateDto: UpdateCandidateDto): Promise<any> {
    try {
      // Step 1: Find the existing person
      const person = await this.peopleRepository.findOne({
        where: { id: candidateId },
        relations: ['jobPreferences', 'emails', 'phones'],
      });

      if (!person) {
        throw new NotFoundException('Candidate not found');
      }

      // Step 2: Update person fields
      if (updateDto.first_name !== undefined) person.first_name = updateDto.first_name;
      if (updateDto.last_name !== undefined) person.last_name = updateDto.last_name;
      if (updateDto.first_name || updateDto.last_name) {
        person.full_name = `${person.first_name || ''} ${person.last_name || ''}`.trim();
      }
      if (updateDto.current_job_title !== undefined) person.current_title = updateDto.current_job_title;
      if (updateDto.li_profile !== undefined) person.profile_url = updateDto.li_profile;
      if (updateDto.currently_employed !== undefined) person.currently_employed = updateDto.currently_employed;
      if (updateDto.additional_information !== undefined) person.additional_information = updateDto.additional_information;
      if (updateDto.preferred_industry !== undefined) person.industry = updateDto.preferred_industry;

      await this.peopleRepository.save(person);

      // Step 3: Update contact information if provided
      if (updateDto.contact !== undefined) {
        await this.updateContactInformation(updateDto.contact, person.id);
      }

      // Step 4: Update or create job preferences
      const hasJobPreferences = this.hasJobPreferencesData(updateDto);
      if (hasJobPreferences) {
        let jobPreferences = person.jobPreferences?.[0];
        
        if (!jobPreferences) {
          // Create new job preferences
          jobPreferences = this.jobPreferencesRepository.create({
            personId: person.id,
          });
        }

        // Update job preferences fields
        if (updateDto.job_title !== undefined) jobPreferences.job_title = updateDto.job_title;
        if (updateDto.job_location !== undefined) jobPreferences.job_location = updateDto.job_location;
        if (updateDto.job_type !== undefined) jobPreferences.job_type = updateDto.job_type;
        if (updateDto.preferred_industry !== undefined) jobPreferences.job_industry = updateDto.preferred_industry;
        if (updateDto.job_experience !== undefined) jobPreferences.job_experience = updateDto.job_experience;
        if (updateDto.availability !== undefined) jobPreferences.availability = updateDto.availability;
        if (updateDto.reason_for_leaving !== undefined) jobPreferences.reason_for_leaving = updateDto.reason_for_leaving;
        if (updateDto.looking_for_time !== undefined) jobPreferences.looking_for_time = updateDto.looking_for_time;
        if (updateDto.job_nature !== undefined) jobPreferences.job_nature = updateDto.job_nature;
        if (updateDto.salary_range !== undefined) jobPreferences.salary_range = updateDto.salary_range;

        await this.jobPreferencesRepository.save(jobPreferences);
      }

      // Step 5: Return the updated candidate data
      return await this.getCandidateById(person.id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error updating candidate:', error);
      throw new InternalServerErrorException('Failed to update candidate');
    }
  }

  async getCandidateById(candidateId: number): Promise<any> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id: candidateId },
        relations: ['jobPreferences', 'emails', 'phones'],
      });

      if (!person) {
        throw new NotFoundException('Candidate not found');
      }

      return this.formatCandidateResponse(person);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error fetching candidate:', error);
      throw new InternalServerErrorException('Failed to fetch candidate');
    }
  }

  async getAllCandidates(page: number = 1, limit: number = 20): Promise<any> {
    try {
      const [candidates, total] = await this.peopleRepository.findAndCount({
        where: { person_type: 'CANDIDATE' },
        relations: ['jobPreferences', 'emails', 'phones'],
        skip: (page - 1) * limit,
        take: limit,
        order: { created_at: 'DESC' },
      });

      return {
        data: candidates.map(candidate => this.formatCandidateResponse(candidate)),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching candidates:', error);
      throw new InternalServerErrorException('Failed to fetch candidates');
    }
  }

  async deleteCandidate(candidateId: number): Promise<{ message: string }> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id: candidateId },
      });

      if (!person) {
        throw new NotFoundException('Candidate not found');
      }

      await this.peopleRepository.remove(person);

      return { message: 'Candidate deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error deleting candidate:', error);
      throw new InternalServerErrorException('Failed to delete candidate');
    }
  }

  // Helper methods
  private hasJobPreferencesData(dto: CandidateDto | UpdateCandidateDto): boolean {
    return !!(
      dto.job_title ||
      dto.job_location ||
      dto.job_type ||
      dto.job_experience ||
      dto.availability ||
      dto.reason_for_leaving ||
      dto.looking_for_time ||
      dto.job_nature ||
      dto.salary_range
    );
  }

  private async handleContactInformation(contact: string, personId: number): Promise<void> {
    // Determine if contact is email or phone
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;

    if (emailRegex.test(contact)) {
      // It's an email
      const email = this.personEmailRepository.create({
        email: contact,
        personId: personId,
      });
      await this.personEmailRepository.save(email);
    } else if (phoneRegex.test(contact.replace(/[\s\-\(\)]/g, ''))) {
      // It's a phone number
      const phone = this.personPhoneRepository.create({
        phone_number: contact,
        personId: personId,
      });
      await this.personPhoneRepository.save(phone);
    }
  }

  private async updateContactInformation(contact: string, personId: number): Promise<void> {
    // Remove existing contact information
    await this.personEmailRepository.delete({ personId });
    await this.personPhoneRepository.delete({ personId });

    // Add new contact information
    await this.handleContactInformation(contact, personId);
  }

  private formatCandidateResponse(person: People): any {
    const jobPreferences = person.jobPreferences?.[0];
    const email = person.emails?.[0]?.email;
    const phone = person.phones?.[0]?.phone_number;

    return {
      id: person.id,
      first_name: person.first_name,
      last_name: person.last_name,
      contact: email || phone || '',
      li_profile: person.profile_url,
      experience: '', // This would need to be calculated from Experience entity
      highest_education: '', // This would need to be calculated from Qualifications entity
      currently_employed: person.currently_employed,
      current_job_title: person.current_title,
      salary_range: jobPreferences?.salary_range || '',
      looking_for_time: jobPreferences?.looking_for_time || [],
      job_type: jobPreferences?.job_type || '',
      job_nature: jobPreferences?.job_nature || '',
      preferred_industry: person.industry || jobPreferences?.job_industry || '',
      additional_information: person.additional_information,
      job_location: jobPreferences?.job_location || '',
      job_title: jobPreferences?.job_title || '',
      job_experience: jobPreferences?.job_experience || '',
      availability: jobPreferences?.availability || '',
      reason_for_leaving: jobPreferences?.reason_for_leaving || '',
      created_at: person.created_at,
      updated_at: person.updated_at,
    };
  }
}
