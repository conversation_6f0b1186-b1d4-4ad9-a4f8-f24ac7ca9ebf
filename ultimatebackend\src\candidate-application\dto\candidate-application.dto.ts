import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEmail } from 'class-validator';

export class CandidateApplicationDto {
  @ApiProperty({
    description: 'First name of the candidate',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  first_name: string;

  @ApiProperty({
    description: 'Last name of the candidate',
    example: '<PERSON><PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  last_name: string;

  @ApiProperty({
    description: 'Contact email of the candidate',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  contact: string;

  @ApiProperty({
    description: 'LinkedIn profile URL',
    example: 'https://linkedin.com/in/johndoe',
    required: false,
  })
  @IsOptional()
  @IsString()
  linkedin_profile?: string;

  @ApiProperty({
    description: 'Preferred job location',
    example: 'London, UK',
  })
  @IsString()
  @IsNotEmpty()
  job_location: string;

  @ApiProperty({
    description: 'Desired job role or position',
    example: 'Software Engineer',
  })
  @IsString()
  @IsNotEmpty()
  desired_job_role: string;

  @ApiProperty({
    description: 'Years of professional experience',
    example: '5 years',
  })
  @IsString()
  @IsNotEmpty()
  years_of_experience: string;

  @ApiProperty({
    description: 'Availability to start work',
    example: 'Immediately',
  })
  @IsString()
  @IsNotEmpty()
  availability: string;

  @ApiProperty({
    description: 'Reason for job change',
    example: 'Career growth and new challenges',
  })
  @IsString()
  @IsNotEmpty()
  reason_for_job_change: string;
}