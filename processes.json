[{"name": "ultimate-backend-dev", "cwd": "E:/1 New CRM/ultimatebackend", "script": "npm", "args": "run start:dev", "interpreter": "none", "exec_mode": "fork", "env": {"NODE_ENV": "development"}, "log_file": "./logs/combined.log", "out_file": "./logs/out.log", "error_file": "./logs/error.log", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "watch": false, "ignore_watch": ["node_modules", "logs"], "restart_delay": 1000, "max_restarts": 10, "min_uptime": "10s"}, {"name": "ultimate-backend-prod", "cwd": "E:/1 New CRM/ultimatebackend", "script": "dist/main.js", "interpreter": "node", "exec_mode": "fork", "env": {"NODE_ENV": "production"}, "log_file": "./logs/prod-combined.log", "out_file": "./logs/prod-out.log", "error_file": "./logs/prod-error.log", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "watch": false, "restart_delay": 1000, "max_restarts": 10, "min_uptime": "10s"}]