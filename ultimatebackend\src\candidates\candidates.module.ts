import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CandidatesController } from './candidates.controller';
import { CandidatesService } from './candidates.service';
import { People } from 'src/people/people.entity';
import { JobPreferences } from 'src/preferences/prefrences.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      People,
      JobPreferences,
      PersonEmail,
      PersonPhone,
    ]),
  ],
  controllers: [CandidatesController],
  providers: [CandidatesService],
  exports: [CandidatesService],
})
export class CandidatesModule {}
