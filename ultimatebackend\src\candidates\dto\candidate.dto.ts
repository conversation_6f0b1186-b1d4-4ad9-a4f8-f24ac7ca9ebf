import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, IsArray, IsEnum } from 'class-validator';

export class CandidateDto {
  // Basic Information
  @ApiProperty({
    description: 'First name of the candidate',
    example: '<PERSON>',
  })
  @IsString()
  first_name: string;

  @ApiProperty({
    description: 'Last name of the candidate',
    example: 'Do<PERSON>',
  })
  @IsString()
  last_name: string;

  @ApiProperty({
    description: 'Contact information (email or phone)',
    example: '<EMAIL>',
  })
  @IsString()
  contact: string;

  @ApiPropertyOptional({
    description: 'LinkedIn profile URL',
    example: 'https://linkedin.com/in/johndoe',
  })
  @IsOptional()
  @IsString()
  li_profile?: string;

  @ApiPropertyOptional({
    description: 'Years of experience',
    example: '5 years',
  })
  @IsOptional()
  @IsString()
  experience?: string;

  @ApiPropertyOptional({
    description: 'Highest education level',
    example: 'Bachelor\'s Degree in Computer Science',
  })
  @IsOptional()
  @IsString()
  highest_education?: string;

  @ApiPropertyOptional({
    description: 'Whether the candidate is currently employed',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  currently_employed?: boolean;

  @ApiPropertyOptional({
    description: 'Current job title',
    example: 'Senior Software Engineer',
  })
  @IsOptional()
  @IsString()
  current_job_title?: string;

  @ApiPropertyOptional({
    description: 'Salary range expectation',
    example: '$80,000 - $120,000',
  })
  @IsOptional()
  @IsString()
  salary_range?: string;

  // Job Preferences
  @ApiPropertyOptional({
    description: 'Looking for part-time or full-time work',
    example: ['Full-Time'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  looking_for_time?: string[]; // Part-Time, Full-Time

  @ApiPropertyOptional({
    description: 'Job type preference',
    example: 'PERMANENT',
    enum: ['PERMANENT', 'CONTRACT', 'TEMPORARY', 'FREELANCE'],
  })
  @IsOptional()
  @IsEnum(['PERMANENT', 'CONTRACT', 'TEMPORARY', 'FREELANCE'])
  job_type?: string; // Permanent, Contract

  @ApiPropertyOptional({
    description: 'Job nature preference',
    example: 'REMOTE',
    enum: ['REMOTE', 'HYBRID', 'ON_SITE'],
  })
  @IsOptional()
  @IsEnum(['REMOTE', 'HYBRID', 'ON_SITE'])
  job_nature?: string; // Remote, Hybrid, On-Site

  @ApiPropertyOptional({
    description: 'Preferred industry',
    example: 'Technology',
  })
  @IsOptional()
  @IsString()
  preferred_industry?: string;

  @ApiPropertyOptional({
    description: 'Additional information about the candidate',
    example: 'Experienced in React, Node.js, and AWS',
  })
  @IsOptional()
  @IsString()
  additional_information?: string;

  @ApiPropertyOptional({
    description: 'Preferred job location',
    example: 'New York, NY',
  })
  @IsOptional()
  @IsString()
  job_location?: string;

  @ApiPropertyOptional({
    description: 'Preferred job title',
    example: 'Software Engineer',
  })
  @IsOptional()
  @IsString()
  job_title?: string;

  @ApiPropertyOptional({
    description: 'Required job experience',
    example: '3-5 years',
  })
  @IsOptional()
  @IsString()
  job_experience?: string;

  @ApiPropertyOptional({
    description: 'Availability to start',
    example: 'Immediately',
  })
  @IsOptional()
  @IsString()
  availability?: string;

  @ApiPropertyOptional({
    description: 'Reason for leaving current position',
    example: 'Career growth',
  })
  @IsOptional()
  @IsString()
  reason_for_leaving?: string;
}

export class UpdateCandidateDto {
  @ApiPropertyOptional({
    description: 'Candidate ID to update',
    example: 123,
  })
  @IsOptional()
  candidateId?: number;

  // All fields from CandidateDto are optional for updates
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  first_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  last_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contact?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  li_profile?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  experience?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  highest_education?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  currently_employed?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  current_job_title?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  salary_range?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  looking_for_time?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(['PERMANENT', 'CONTRACT', 'TEMPORARY', 'FREELANCE'])
  job_type?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(['REMOTE', 'HYBRID', 'ON_SITE'])
  job_nature?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  preferred_industry?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  additional_information?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  job_location?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  job_title?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  job_experience?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  availability?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  reason_for_leaving?: string;
}
